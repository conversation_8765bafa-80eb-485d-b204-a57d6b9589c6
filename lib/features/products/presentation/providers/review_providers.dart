import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/review.dart';

// Product Reviews Provider - fetches reviews for a specific product
final productReviewsProvider = FutureProvider.family<List<Review>, String>((
  ref,
  productId,
) async {
  // Simulate network delay
  await Future.delayed(const Duration(milliseconds: 500));
});
// Review Summary Provider - provides aggregated review data
final reviewSummaryProvider = FutureProvider.family<ReviewSummary, String>((
  ref,
  productId,
) async {
  final reviews = await ref.watch(productReviewsProvider(productId).future);

  if (reviews.isEmpty) {
    return const ReviewSummary(
      averageRating: 0.0,
      totalReviews: 0,
      ratingDistribution: {},
    );
  }

  final totalReviews = reviews.length;
  final totalRating = reviews.fold<double>(
    0.0,
    (sum, review) => sum + review.rating,
  );
  final averageRating = totalRating / totalReviews;

  // Calculate rating distribution
  final ratingDistribution = <int, int>{};
  for (int i = 1; i <= 5; i++) {
    ratingDistribution[i] = reviews.where((r) => r.rating.round() == i).length;
  }

  return ReviewSummary(
    averageRating: averageRating,
    totalReviews: totalReviews,
    ratingDistribution: ratingDistribution,
  );
});

// Review Summary data class
class ReviewSummary {
  final double averageRating;
  final int totalReviews;
  final Map<int, int> ratingDistribution;

  const ReviewSummary({
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
  });
}
